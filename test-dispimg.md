# DISPIMG图片解析解决方案

## 问题描述
WPS中嵌入单元格的图片使用DISPIMG函数，这种图片无法通过ExcelJS的`worksheet.getImages()`方法直接获取，只能识别出自定义函数的值。

## 解决方案

### 核心思路
根据GitHub issue #2786的解决方案，需要解析Excel文件内部的XML结构来获取DISPIMG图片的映射关系。

### 关键文件
1. `xl/cellimages.xml` - 存储嵌入式图片的元数据
2. `xl/_rels/cellimages.xml.rels` - 存储图片资源的关系映射

### 实现步骤

#### 1. 解析XML结构
```javascript
const parseExcelInternalStructure = async (arrayBuffer, workbook) => {
  // 使用JSZip解压Excel文件
  const zip = new JSZip();
  const zipContent = await zip.loadAsync(arrayBuffer);
  
  // 检查cellimages相关文件
  const cellimagesXmlFile = zipContent.file("xl/cellimages.xml");
  const cellimagesRelsFile = zipContent.file("xl/_rels/cellimages.xml.rels");
  
  if (!cellimagesXmlFile || !cellimagesRelsFile) {
    return null; // 使用传统方法
  }
  
  // 解析XML内容并建立映射关系
  // ...
}
```

#### 2. 建立图片映射关系
```javascript
// 处理cellimages.xml中的图片信息
const cellimagesNameRidMap = {};
cellimagesData['etc:cellImages']['etc:cellImage'].forEach((pic) => {
  const rid = pic['xdr:pic'][0]['xdr:blipFill'][0]['a:blip'][0].$['r:embed'];
  const imageId = pic['xdr:pic'][0]['xdr:nvPicPr'][0]['xdr:cNvPr'][0].$.name;
  cellimagesNameRidMap[rid] = imageId;
});

// 关联媒体资源
for (const relationship of cellimagesRelsData['Relationships']['Relationship']) {
  const data = relationship.$.Target.match(/media\/([a-zA-Z0-9]+[.][a-zA-Z0-9]{3,4})$/);
  if (!data) continue;
  
  const fullName = data[1];
  const [name, extension] = fullName.split('.');
  const imageId = cellimagesNameRidMap[relationship.$.Id];
  
  if (imageId) {
    const mediaResource = workbook.media.find(v => v.name === name);
    if (mediaResource) {
      result[imageId] = mediaResource;
    }
  }
}
```

#### 3. 处理DISPIMG公式
```javascript
if (!imageUrl && cell.formula?.includes("DISPIMG")) {
  // 1. 尝试精确位置匹配（传统方法）
  const imageId = positionMap.get(positionKey);
  if (imageId !== undefined && imageIdToUrlMap.has(imageId)) {
    imageUrl = imageIdToUrlMap.get(imageId);
  }
  // 2. 使用XML解析的映射关系
  else if (dispimgMapping && cell.formula) {
    const dispimgMatch = cell.formula.match(/DISPIMG\("([^"]+)"/);
    if (dispimgMatch) {
      const imageName = dispimgMatch[1];
      const mediaResource = dispimgMapping[imageName];
      if (mediaResource) {
        const mediaIndex = allImages.findIndex(img => img.media === mediaResource);
        if (mediaIndex !== -1 && imageIdToUrlMap.has(mediaIndex)) {
          imageUrl = imageIdToUrlMap.get(mediaIndex);
        }
      }
    }
  }
  // 3. 备用方法：根据图片名匹配
  else if (cell.formula) {
    // 尝试根据图片名匹配
    // ...
  }
}
```

## 优势
1. **准确性** - 直接解析Excel内部结构，获得准确的图片映射关系
2. **兼容性** - 支持WPS生成的DISPIMG函数
3. **可靠性** - 不依赖硬编码映射，适用于各种Excel文件
4. **性能** - 避免重复图片的重复上传

## 测试方法
1. 准备包含DISPIMG函数的Excel文件
2. 上传文件并观察控制台日志
3. 检查图片是否正确显示在表格中
4. 验证重复图片是否正确处理

## 注意事项
1. 需要安装`jszip`和`xml2js`依赖
2. 如果Excel文件不包含cellimages相关文件，会自动回退到传统方法
3. 建议在生产环境中添加更多错误处理和日志记录
